import logging
import requests
from collections.abc import Mapping

from dify_plugin import <PERSON><PERSON>rovider
from dify_plugin.errors.model import CredentialsValidateFailedError

logger = logging.getLogger(__name__)


class PrompttModelProvider(ModelProvider):
    def validate_provider_credentials(self, credentials: Mapping) -> None:
        """
        Validate provider credentials by making a test API call

        :param credentials: provider credentials, credentials form defined in `provider_credential_schema`.
        """
        try:
            api_key = credentials.get('api_key')
            if not api_key:
                raise CredentialsValidateFailedError('API key is required')

            # Validate API key format (should be AppID:AppKey)
            if ':' not in api_key:
                raise CredentialsValidateFailedError('API key should be in format AppID:AppKey')

            # Test API connection with a simple request
            self._test_api_connection(api_key)

        except CredentialsValidateFailedError:
            raise
        except Exception as ex:
            logger.exception(f"Promptt credentials validation failed: {str(ex)}")
            raise CredentialsValidateFailedError(f"Invalid credentials: {str(ex)}")

    def _test_api_connection(self, api_key: str) -> None:
        """Test API connection with the provided credentials."""
        try:
            # Use the same endpoint as defined in llm.py
            base_url = "http://aigc.cn-bj.rcmd-tt.skyengine.net.cn/eliza/v1"

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            # Make a simple request to test the connection
            response = requests.get(
                f"{base_url}/models",
                headers=headers,
                timeout=10
            )

            if response.status_code == 401:
                raise CredentialsValidateFailedError('Invalid API key')
            elif response.status_code != 200:
                raise CredentialsValidateFailedError(f'API connection failed with status {response.status_code}')

        except requests.exceptions.RequestException as e:
            logger.warning(f"API connection test failed: {str(e)}")
            # Don't fail validation for network issues, just log the warning
            pass
