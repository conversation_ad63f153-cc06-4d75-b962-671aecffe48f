provider: promptt
label:
  en_US: Promptt
  zh_Hans: 泼墨体
icon_small:
  en_US: icon.png
icon_large:
  en_US: icon.png
background: "#E5E7EB"
help:
  title:
    en_US: Get your API Key from PromptT
    zh_Hans: 从「泼墨体」获取 API Key（格式为`{AppID}:{AppKey}`)
  url:
    en_US: https://aigc.skyengine.com.cn/promptt/#/statistics-manage/project-statistics
supported_model_types:
  - llm
configurate_methods:
  - predefined-model
  - customizable-model
model_credential_schema:
  credential_form_schemas:
    - label:
        en_US: API Key
      placeholder:
        en_US: Enter your API Key
        zh_Hans: 在此输入您的 API Key
      required: true
      type: secret-input
      variable: api_key
    - default: "4096"
      label:
        en_US: Model context size
        zh_Hans: 模型上下文长度
      placeholder:
        en_US: Enter your Model context size
        zh_Hans: 在此输入您的模型上下文长度
      required: true
      type: text-input
      variable: context_size
    - default: "4096"
      label:
        en_US: Upper bound for max tokens
        zh_Hans: 最大 token 上限
      show_on:
        - value: llm
          variable: __model_type
      type: text-input
      variable: max_tokens
    - default: no_call
      label:
        en_US: Function calling
      options:
        - label:
            en_US: Not Support
            zh_Hans: 不支持
          value: no_call
        - label:
            en_US: Support
            zh_Hans: 支持
          value: function_call
      required: false
      show_on:
        - value: llm
          variable: __model_type
      type: select
      variable: function_calling_type
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter your model name
      zh_Hans: 输入模型名称
provider_credential_schema:
  credential_form_schemas:
    - variable: api_key
      label:
        en_US: API Key
      type: secret-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
models:
  llm:
    position: models/llm/_position.yaml
    predefined:
      - "models/llm/*.yaml"
extra:
  python:
    provider_source: provider/promptt.py
    model_sources:
      - "models/llm/llm.py"
