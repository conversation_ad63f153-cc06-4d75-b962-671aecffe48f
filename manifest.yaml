version: 0.0.6
type: plugin
author: wuwei
name: promptt
label:
  en_US: PromptT
  zh_Hans: 泼墨体
description:
  en_US: PromptT provides access to various models (LLMs, text embedding, reranking), configurable via model name, API key, and other parameters.
  zh_Hans: 泼墨体提供对各种模型（LLM、文本嵌入、重排序）的访问，可通过模型名称、API密钥和其他参数进行配置。
icon: icon.png
resource:
  memory: 268435456
  permission:
    tool:
      enabled: false
    model:
      enabled: true
      llm: true
      text_embedding: true
      rerank: true
      tts: false
      speech2text: false
      moderation: false
plugins:
  models:
    - provider/promptt.yaml
meta:
  version: 0.0.1
  arch:
    - amd64
    - arm64
  runner:
    language: python
    version: "3.12"
    entrypoint: main
  minimum_dify_version: 0.0.1
created_at: 2025-06-13T14:47:43.40125+08:00
privacy: PRIVACY.md
repo: https://gitlab.ttyuyin.com/wuwei/dify-plugin-models_promptt
verified: false
