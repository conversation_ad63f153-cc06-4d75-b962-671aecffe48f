model: gpt-4o
label:
  en_US: gpt-4o
model_type: llm
features:
- tool-call
- vision
- structured-output
model_properties:
  mode: chat
  context_size: 128000
parameter_rules:
- name: temperature
  type: float
  required: false
  label:
    en_US: 温度temperature
    zh_Hans: 温度temperature
  help:
    en_US: 控制输出的随机性
    zh_Hans: 控制输出的随机性
  default: 1.0
  min: 0.0
  max: 2.0
  precision: 0.1
pricing:
  input: '5.0'
  output: '15.0'
  unit: '0.000001'
  currency: CNY
