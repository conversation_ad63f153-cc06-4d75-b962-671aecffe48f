model: gemini-2.5-pro-preview-05-06
label:
  en_US: gemini-2.5-pro-preview-05-06
model_type: llm
features:
- tool-call
- structured-output
- stream-tool-call
- audio
- agent-thought
- document
- video
- vision
model_properties:
  mode: chat
  context_size: 1048576
parameter_rules:
- name: temperature
  type: float
  required: true
  label:
    en_US: 温度temperature
    zh_Hans: 温度temperature
  help:
    en_US: 采样环节的参数，用于控制随机性。较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定
    zh_Hans: 采样环节的参数，用于控制随机性。较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定
  default: 1.0
  min: 0.0
  max: 2.0
  precision: 0.1
pricing:
  input: '9.0'
  output: '72.0'
  unit: '0.000001'
  currency: CNY
