{"total": 2, "models": [{"model": {"id": "doubao-seed-1-6-flash-250615_1", "name": "doubao-seed-1-6-flash-250615", "alias": "doubao-seed-1-6-flash-250615", "kind": "do<PERSON>o", "avatar": "https://aigc-ops.skyengine.com.cn/v1/file/avatar?filename=zssu9e-sxqt6k.png", "modelType": "TXT_TO_TXT_MODEL", "supportedEndpoints": [], "tokenEncoding": "gpt-3.5-turbo", "tokenLimitType": 2, "inputMaxToken": 224000, "outputMaxToken": 16384, "maxToken": 0, "promptUnitPrice": 6666667, "completionUnitPrice": 666667, "method": {"methodId": 1029, "txt": {"unit": "TbPerKToken", "inputTxt": 0.00015, "outputTxt": 0.0015, "cachedTxt": 0}, "img": {"unit": "TbPerImg", "inputImg": 0, "outputImg": 0}, "video": {"unit": "TbPerSec", "inputVideo": 0, "outputVideo": 0}, "tts": {"unit": "TbPerKToken", "inputTts": 0}, "host": {"unit": "TbPerDay", "hostPerDay": 0}, "audio": {"unit": "TbPerSec", "inputAudio": 0, "outputAudio": 0}, "discount": 100}, "state": "STATE_ONLINE", "updatedAt": "1749731388", "sort": 0, "modelVersion": 1, "modelMetaData": {"base_model": "", "fine_tuning": "", "rpm_limit": "30000", "tpm_limit": "5000000", "version": "1"}, "subType": "ModelSubTypeZero", "resource": "Business", "industries": ["通用"], "manufacturer": "字节跳动", "creatorId": "T3495", "creatorName": "吴辉扬", "createAt": "2025-06-12 20:29:47", "onlineAt": "2025-06-12 20:29:47", "offlineAt": "", "describe": "Doubao-Seed-1.6-flash推理速度极致的多模态深度思考模型", "versionDescribe": "", "opsModelId": "684ac77bf3ca220001acb13e", "opsVersionId": "684ac77bf3ca220001acb13f", "platformType": "IsPlatform", "isUsable": true, "isOpsModel": true, "opsId": "684ac83bf3ca220001acb148", "defaultWeight": 0.5, "modelServiceId": "684ac83bf3ca220001acb148", "modelServiceName": "doubao-seed-1-6-flash-250615", "modelServiceDescribe": "", "modelServiceAlias": "doubao-seed-1.6-flash-250615", "characteristics": ["FunctionCall", "Stream", "JsonObject", "System", "DeepThink"], "modelLimit": {"multimodals": ["Text", "Img", "Video"], "text": {"encoding": "gpt-3.5-turbo", "tokenLimitType": "InOutAlone", "maxToken": 0, "inputMaxToken": 224000, "outputMaxToken": 16384}, "img": {"formats": ["ImgFormatZero"], "size": 0, "count": 0}, "video": {"formats": ["VideoFormatZero"], "duration": 0, "count": 0}, "audio": null, "file": null}, "modelParameters": {"modelParameters": [{"name": "temperature", "alias": "温度temperature", "description": "采样温度。控制了生成文本时对每个候选词的概率分布进行平滑的程度。", "type": "Float", "required": false, "show": true, "min": "0", "max": "1", "default": "1", "options": [], "precision": "0.1", "subParameters": []}, {"name": "top_p", "alias": "核采样top_p", "description": "控制生成多样性， 影响输出文本的多样性，取值越大，生成文本的多样性越强；建议该参数和temperature只设置其中一个", "type": "Float", "required": false, "show": true, "min": "0", "max": "1.0", "default": "0.7", "options": [], "precision": "0.1", "subParameters": []}]}, "timbres": [], "manufacturerSort": 2, "frame": "", "label": "LatestOnline", "expectedOfflineTime": "0"}}, {"model": {"id": "gpt-4o_1", "name": "gpt-4o", "alias": "gpt-4o", "kind": "openai", "avatar": "https://aigc-ops.skyengine.com.cn/v1/file/avatar?filename=openai.png", "modelType": "TXT_TO_TXT_MODEL", "supportedEndpoints": [], "tokenEncoding": "gpt-4", "tokenLimitType": 2, "inputMaxToken": 128000, "outputMaxToken": 4096, "maxToken": 0, "promptUnitPrice": 222222, "completionUnitPrice": 33333, "method": {"methodId": 955, "txt": {"unit": "TbPerKToken", "inputTxt": 0.005, "outputTxt": 0.015, "cachedTxt": 0}}, "state": "STATE_ONLINE", "updatedAt": "1749110468", "sort": 1, "modelVersion": 1, "modelServiceId": "6825841306cd1700018fac7c", "modelServiceName": "gpt-4o", "modelServiceDescribe": "", "modelServiceAlias": "gpt-4o", "characteristics": ["FunctionCall", "JsonSchema"], "modelLimit": {"multimodals": ["Text", "Img"], "text": {"encoding": "gpt-4", "tokenLimitType": "InOutAlone", "maxToken": 0, "inputMaxToken": 128000, "outputMaxToken": 4096}}, "modelParameters": {"modelParameters": [{"name": "temperature", "alias": "温度temperature", "description": "控制输出的随机性", "type": "Float", "required": false, "show": true, "min": "0", "max": "2", "default": "1", "options": [], "precision": "0.1", "subParameters": []}]}}}]}