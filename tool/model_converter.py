#!/usr/bin/env python3
"""
Promptt Model to Dify AI Model Entity Converter

This script converts Promptt model JSON format to Dify AI model entity YAML format.
"""

import json
import yaml
import sys
from typing import Dict, List, Any, Optional
from pathlib import Path
from dify_plugin.entities.model import ModelFeature


class PrompttModelConverter:
    """Converter class for transforming Promptt model JSON to Dify AI model YAML."""

    # Mapping from Promptt characteristics to Dify features
    CHARACTERISTIC_TO_FEATURE_MAP = {
        'FunctionCall': ModelFeature.TOOL_CALL,
        'JsonSchema': ModelFeature.STRUCTURED_OUTPUT,
        'DeepThink': ModelFeature.AGENT_THOUGHT,
    }

    # Mapping from Promptt multimodals to Dify features
    MULTIMODAL_TO_FEATURE_MAP = {
        'Text': None,  # Base functionality, no specific feature
        'Img': ModelFeature.VISION,
        'Video': ModelFeature.VIDEO,
        'Audio': ModelFeature.AUDIO,
        'File': ModelFeature.DOCUMENT,
    }

    # Mapping from Promptt model types to Dify model types
    MODEL_TYPE_MAP = {
        'TXT_TO_TXT_MODEL': 'llm',
    }

    def __init__(self):
        pass

    def convert_features(self, model_data: Dict[str, Any]) -> List[str]:
        """Convert Promptt characteristics and multimodals to Dify features."""
        features = set()

        # Convert characteristics
        characteristics = model_data.get('characteristics', [])
        for char in characteristics:
            if char in self.CHARACTERISTIC_TO_FEATURE_MAP:
                feature = self.CHARACTERISTIC_TO_FEATURE_MAP[char]
                if feature:
                    features.add(feature.value)

        # Special case: only add stream-tool-call when both FunctionCall and Stream are present
        if 'FunctionCall' in characteristics and 'Stream' in characteristics:
            features.add(ModelFeature.STREAM_TOOL_CALL.value)

        # Convert multimodals
        multimodals = model_data.get('modelLimit', {}).get('multimodals', [])
        for modal in multimodals:
            if modal in self.MULTIMODAL_TO_FEATURE_MAP:
                feature = self.MULTIMODAL_TO_FEATURE_MAP[modal]
                if feature:
                    features.add(feature.value)

        return list(features)

    def convert_parameter_type(self, promptt_type: str) -> str:
        """Convert Promptt parameter type to Dify parameter type."""
        type_map = {
            'Float': 'float',
            'Int': 'int',
            'String': 'string',
            'Boolean': 'boolean'
        }
        return type_map.get(promptt_type, 'string')

    def convert_parameters(self, model_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert Promptt model parameters to Dify parameter rules."""
        parameters = []
        model_params = model_data.get('modelParameters', {}).get('modelParameters', [])

        for param in model_params:
            param_rule = {
                'name': param.get('name'),
                'type': self.convert_parameter_type(param.get('type', 'String')),
                'required': param.get('required', False)
            }

            # Add label if alias exists
            if param.get('alias'):
                param_rule['label'] = {
                    'en_US': param['alias'],
                    'zh_Hans': param['alias']
                }

            # Add description as help
            if param.get('description'):
                param_rule['help'] = {
                    'en_US': param['description'],
                    'zh_Hans': param['description']
                }

            # Add default value
            if param.get('default') is not None:
                try:
                    if param_rule['type'] == 'float':
                        param_rule['default'] = float(param['default'])
                    elif param_rule['type'] == 'int':
                        param_rule['default'] = int(param['default'])
                    else:
                        param_rule['default'] = param['default']
                except (ValueError, TypeError):
                    param_rule['default'] = param['default']

            # Add min/max values
            if param.get('min') is not None:
                try:
                    param_rule['min'] = float(param['min']) if param_rule['type'] == 'float' else int(param['min'])
                except (ValueError, TypeError):
                    pass

            if param.get('max') is not None:
                try:
                    param_rule['max'] = float(param['max']) if param_rule['type'] == 'float' else int(param['max'])
                except (ValueError, TypeError):
                    pass

            # Add precision for float types
            if param_rule['type'] == 'float' and param.get('precision'):
                try:
                    param_rule['precision'] = float(param['precision'])
                except (ValueError, TypeError):
                    pass

            parameters.append(param_rule)

        return parameters

    def convert_pricing(self, model_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Convert Promptt pricing to Dify pricing format."""
        method = model_data.get('method', {})
        txt_pricing = method.get('txt', {})

        if not txt_pricing:
            return None

        # Extract pricing information
        input_price = txt_pricing.get('inputTxt', 0)
        output_price = txt_pricing.get('outputTxt', 0)
        unit = txt_pricing.get('unit', '')

        # Only provide pricing if unit is TbPerKToken (per thousand tokens)
        if unit != 'TbPerKToken':
            return None

        # Convert from per thousand tokens to per million tokens
        # Original price is per 1000 tokens, convert to per 1,000,000 tokens
        input_price_per_million = input_price * 1000  # 1000 * 1000 / 1000 = 1000
        output_price_per_million = output_price * 1000

        pricing = {
            'input': str(input_price_per_million),
            'output': str(output_price_per_million),
            'unit': '0.000001',  # Per million tokens
            'currency': 'CNY'  # Chinese Yuan (RMB)
        }

        return pricing

    def convert(self, promptt_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Promptt model JSON to Dify AI model YAML format."""
        model_data = promptt_data.get('model', {})

        if not model_data:
            raise ValueError("No 'model' key found in Promptt data")

        # Basic model information
        model_service_name = model_data.get('modelServiceName', model_data.get('name', ''))
        kind = model_data.get('kind', '')
        name = model_data.get('name', '')

        # Use modelServiceName as model field
        model_field = model_service_name

        # Use {kind}/{name} as label.en_US
        label_en_us = f"{kind}/{name}" if kind and name else model_service_name

        dify_model = {
            'model': model_field,
            'label': {
                'en_US': label_en_us
            },
            'model_type': self.MODEL_TYPE_MAP.get(model_data.get('modelType', 'TXT_TO_TXT_MODEL'), 'llm')
        }

        # Convert features
        features = self.convert_features(model_data)
        if features:
            dify_model['features'] = features

        # Model properties
        model_properties = {
            'mode': 'chat'  # Default mode for LLM
        }

        # Add context size
        context_size = model_data.get('inputMaxToken')
        if context_size:
            model_properties['context_size'] = context_size

        dify_model['model_properties'] = model_properties

        # Convert parameters
        parameters = self.convert_parameters(model_data)
        if parameters:
            dify_model['parameter_rules'] = parameters

        # Convert pricing
        pricing = self.convert_pricing(model_data)
        if pricing:
            dify_model['pricing'] = pricing

        return dify_model

    def convert_file(self, input_file: str, output_file: str = None) -> str:
        """Convert a Promptt JSON file to Dify YAML file."""
        input_path = Path(input_file)

        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_file}")

        # Read input JSON
        with open(input_path, 'r', encoding='utf-8') as f:
            promptt_data = json.load(f)

        # Convert to Dify format
        dify_data = self.convert(promptt_data)

        # Generate output filename if not provided
        if output_file is None:
            model_name = dify_data.get('model', 'converted_model')
            output_file = f"{model_name}.yaml"

        output_path = Path(output_file)

        # Write output YAML
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(dify_data, f, default_flow_style=False, allow_unicode=True, sort_keys=False)

        return str(output_path)

    def convert_batch_file(self, input_file: str, output_dir: str = None) -> List[str]:
        """Convert a batch Promptt JSON file containing multiple models to multiple Dify YAML files."""
        input_path = Path(input_file)

        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_file}")

        # Read input JSON
        with open(input_path, 'r', encoding='utf-8') as f:
            batch_data = json.load(f)

        # Validate batch format
        if 'models' not in batch_data:
            raise ValueError("Batch file must contain 'models' key")

        models = batch_data.get('models', [])
        total = batch_data.get('total', len(models))

        print(f"Processing {len(models)} models (total: {total})")

        # Set output directory
        if output_dir is None:
            output_dir = input_path.parent / "converted_models"
        else:
            output_dir = Path(output_dir)

        # Create output directory if it doesn't exist
        output_dir.mkdir(exist_ok=True)

        converted_files = []
        failed_conversions = []

        for i, model_data in enumerate(models):
            try:
                # Wrap single model in the expected format
                promptt_data = {"model": model_data}

                # Convert to Dify format
                dify_data = self.convert(promptt_data)

                # Generate output filename based on model name
                model_name = dify_data.get('model', f'model_{i}')
                # Sanitize filename
                safe_model_name = "".join(c for c in model_name if c.isalnum() or c in ('-', '_', '.')).rstrip()
                output_file = output_dir / f"{safe_model_name}.yaml"

                # Write output YAML
                with open(output_file, 'w', encoding='utf-8') as f:
                    yaml.dump(dify_data, f, default_flow_style=False, allow_unicode=True, sort_keys=False)

                converted_files.append(str(output_file))
                print(f"✓ Converted: {model_name} -> {output_file}")

            except Exception as e:
                error_msg = f"✗ Failed to convert model {i}: {str(e)}"
                print(error_msg)
                failed_conversions.append(error_msg)

        print(f"\nConversion completed:")
        print(f"  Successfully converted: {len(converted_files)} models")
        print(f"  Failed conversions: {len(failed_conversions)} models")

        if failed_conversions:
            print("\nFailed conversions:")
            for error in failed_conversions:
                print(f"  {error}")

        return converted_files


def main():
    """Main CLI function."""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Single model: python model_converter.py <input_json_file> [output_yaml_file]")
        print("  Batch mode:   python model_converter.py --batch <batch_json_file> [output_directory]")
        print("")
        print("Examples:")
        print("  python model_converter.py promptt_model_example.json gemini-model.yaml")
        print("  python model_converter.py --batch batch_models.json ./converted_models/")
        sys.exit(1)

    # Check for batch mode
    if sys.argv[1] == '--batch':
        if len(sys.argv) < 3:
            print("Error: Batch mode requires input file")
            print("Usage: python model_converter.py --batch <batch_json_file> [output_directory]")
            sys.exit(1)

        input_file = sys.argv[2]
        output_dir = sys.argv[3] if len(sys.argv) > 3 else None

        try:
            converter = PrompttModelConverter()
            converted_files = converter.convert_batch_file(input_file, output_dir)
            print(f"\nBatch conversion completed. Generated {len(converted_files)} files.")
        except Exception as e:
            print(f"Error: {e}")
            sys.exit(1)
    else:
        # Single model mode
        input_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None

        try:
            converter = PrompttModelConverter()
            output_path = converter.convert_file(input_file, output_file)
            print(f"Successfully converted {input_file} to {output_path}")
        except Exception as e:
            print(f"Error: {e}")
            sys.exit(1)


if __name__ == "__main__":
    main()
