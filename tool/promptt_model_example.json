{"model": {"id": "gemini-2.5-pro-preview-05-06_1", "name": "gemini-2.5-pro-preview-05-06", "alias": "gemini-2.5-pro-preview-05-06", "kind": "gemini", "avatar": "https://aigc-ops.skyengine.com.cn/v1/file/avatar?filename=k2vml3-swafgy.png", "modelType": "TXT_TO_TXT_MODEL", "supportedEndpoints": [], "tokenEncoding": "gpt-3.5-turbo", "tokenLimitType": 2, "inputMaxToken": 1048576, "outputMaxToken": 65535, "maxToken": 0, "promptUnitPrice": 111111, "completionUnitPrice": 13889, "method": {"methodId": 954, "txt": {"unit": "TbPerKToken", "inputTxt": 0.009, "outputTxt": 0.072, "cachedTxt": 0}, "img": {"unit": "TbPerImg", "inputImg": 0, "outputImg": 0}, "video": {"unit": "TbPerSec", "inputVideo": 0, "outputVideo": 0}, "tts": {"unit": "TbPerKToken", "inputTts": 0}, "host": {"unit": "TbPerDay", "hostPerDay": 0}, "audio": {"unit": "TbPerSec", "inputAudio": 0, "outputAudio": 0}, "discount": 100}, "state": "STATE_ONLINE", "updatedAt": "1749110467", "sort": 0, "modelVersion": 1, "modelMetaData": {"base_model": "", "fine_tuning": "", "rpm_limit": "0", "tpm_limit": "0", "version": "1"}, "subType": "ModelSubTypeZero", "resource": "Business", "industries": ["通用"], "manufacturer": "谷歌", "creatorId": "xxx", "creatorName": "xxx", "createAt": "2025-05-15 14:05:07", "onlineAt": "2025-05-15 14:05:07", "offlineAt": "", "describe": "", "versionDescribe": "", "opsModelId": "68257d1b06cd1700018fac1d", "opsVersionId": "68257d1b06cd1700018fac1e", "platformType": "IsPlatform", "isUsable": true, "isOpsModel": true, "opsId": "6825841306cd1700018fac7b", "defaultWeight": 0.5, "modelServiceId": "6825841306cd1700018fac7b", "modelServiceName": "gemini-2.5-pro-preview-05-06", "modelServiceDescribe": "", "modelServiceAlias": "gemini-2.5-pro-preview-05-06", "characteristics": ["FunctionCall", "Stream", "JsonSchema", "System", "DeepThink"], "modelLimit": {"multimodals": ["Text", "Img", "Video", "Audio", "File"], "text": {"encoding": "gpt-3.5-turbo", "tokenLimitType": "InOutAlone", "maxToken": 0, "inputMaxToken": 1048576, "outputMaxToken": 65535}, "img": {"formats": ["Png", "Jpeg", "<PERSON><PERSON>"], "size": 7, "count": 3000}, "video": {"formats": ["VideoFormatZero"], "duration": 0, "count": 10}, "audio": {"formats": ["AudioFormatZero"], "duration": 0, "count": 1}, "file": {"formats": ["Pdf", "Txt"], "size": 50, "count": 3000}}, "modelParameters": {"modelParameters": [{"name": "temperature", "alias": "温度temperature", "description": "采样环节的参数，用于控制随机性。较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定", "type": "Float", "required": true, "show": true, "min": "0", "max": "2", "default": "1", "options": [], "precision": "0.1", "subParameters": []}]}, "timbres": [], "manufacturerSort": 8, "frame": "", "label": "LatestOnline", "expectedOfflineTime": "0"}, "isAdded": false, "releaseResources": ["Platform"], "addeds": [], "unitPrice": null, "order": 100, "evalScore": 0}