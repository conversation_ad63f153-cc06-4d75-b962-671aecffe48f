model: wenxin-chatV1.0.01704425253066
label:
  en_US: wenxin/wenxin-chat
model_type: llm
model_properties:
  mode: chat
parameter_rules:
- name: temperature
  type: float
  required: true
  label:
    en_US: 温度temperature
    zh_Hans: 温度temperature
  help:
    en_US: 采样环节的参数，用于控制随机性。较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定
    zh_Hans: 采样环节的参数，用于控制随机性。较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定
  default: 0.8
  min: 0.01
  max: 1.0
  precision: 0.1
- name: top_p
  type: float
  required: true
  label:
    en_US: 核采样top_p
    zh_Hans: 核采样top_p
  help:
    en_US: 控制生成多样性， 影响输出文本的多样性，取值越大，生成文本的多样性越强；建议该参数和temperature只设置其中一个
    zh_Hans: 控制生成多样性， 影响输出文本的多样性，取值越大，生成文本的多样性越强；建议该参数和temperature只设置其中一个
  default: 0.8
  min: 0.0
  max: 1.0
  precision: 0.1
- name: frequency_penalty
  type: float
  required: true
  label:
    en_US: 重复惩罚度
    zh_Hans: 重复惩罚度
  help:
    en_US: 通过对已生成的token增加惩罚，减少重复生成的现象，值越高则惩罚越大
    zh_Hans: 通过对已生成的token增加惩罚，减少重复生成的现象，值越高则惩罚越大
  default: 1.0
  min: 1.0
  max: 2.0
  precision: 0.1
pricing:
  input: '12.0'
  output: '12.0'
  unit: '0.000001'
  currency: CNY
