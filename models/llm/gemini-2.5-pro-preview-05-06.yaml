# https://ai.google.dev/gemini-api/docs/models?hl=zh-cn#gemini-2.5-pro-preview-05-06
model: gemini-2.5-pro-preview-05-06
label:
  en_US: gemini-2.5-pro-preview-05-06
model_type: llm
features:
  - agent-thought
  - vision
  - tool-call
  - stream-tool-call
  - document
  - video
  - audio
model_properties:
  mode: chat
  context_size: 1048576
parameter_rules:
  - name: temperature
    use_template: temperature
    default: 1
    min: 0
    max: 2
  - name: top_p
    use_template: top_p
  - name: top_k
    label:
      zh_Hans: 取样数量
      en_US: Top k
    type: int
    help:
      zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
      en_US: Only sample from the top K options for each subsequent token.
    required: false
  - name: max_output_tokens
    use_template: max_tokens
    default: 65535
    min: 1
    max: 65536
  - name: grounding
    label:
      en_US: Grounding
      zh_Hans: 事实核查
      ja_JP: 事実チェック
    type: boolean
    help:
      en_US: Grounding with Google Search
      zh_Hans: Google 事实核查
      ja_JP: Google検索に基づいた応答をします。
    required: true
    default: false
  - name: json_schema
    use_template: json_schema
# https://ai.google.dev/gemini-api/docs/pricing?hl=zh-cn#gemini-2.5-pro-preview
pricing:
  input: '1.25'
  output: '10.00'
  unit: '0.000001'
  currency: USD
