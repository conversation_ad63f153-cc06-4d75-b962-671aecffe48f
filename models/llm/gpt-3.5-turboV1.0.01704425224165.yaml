model: gpt-3.5-turboV1.0.01704425224165
label:
  en_US: azure/gpt-3.5-turbo
model_type: llm
features:
- stream-tool-call
- tool-call
model_properties:
  mode: chat
parameter_rules:
- name: temperature
  type: float
  required: false
  label:
    en_US: 温度temperature
    zh_Hans: 温度temperature
  help:
    en_US: 采样环节的参数，用于控制随机性。较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定
    zh_Hans: 采样环节的参数，用于控制随机性。较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定
  default: 1.0
  min: 0.0
  max: 2.0
  precision: 0.1
- name: top_p
  type: float
  required: false
  label:
    en_US: 核采样top_p
    zh_Hans: 核采样top_p
  help:
    en_US: 控制生成多样性， 影响输出文本的多样性，取值越大，生成文本的多样性越强；建议该参数和temperature只设置其中一个
    zh_Hans: 控制生成多样性， 影响输出文本的多样性，取值越大，生成文本的多样性越强；建议该参数和temperature只设置其中一个
  default: 1.0
  min: 0.0
  max: 2.0
  precision: 0.1
- name: frequency_penalty
  type: float
  required: false
  label:
    en_US: 频率惩罚度
    zh_Hans: 频率惩罚度
  help:
    en_US: 影响常见词和罕见词汇使用。值较大时,倾向于生成不常见的词汇和表达方式;值越小,更倾向于使用常见和普遍接受的词汇或短语
    zh_Hans: 影响常见词和罕见词汇使用。值较大时,倾向于生成不常见的词汇和表达方式;值越小,更倾向于使用常见和普遍接受的词汇或短语
  default: 0.0
  min: -2.0
  max: 2.0
  precision: 0.1
- name: presence_penalty
  type: float
  required: false
  label:
    en_US: 存在惩罚度
    zh_Hans: 存在惩罚度
  help:
    en_US: 设置可防止模型在其响应中过于频繁地重复短语。值越大，越可能使用新的短语，值越小，模型越可能使用重复的短语
    zh_Hans: 设置可防止模型在其响应中过于频繁地重复短语。值越大，越可能使用新的短语，值越小，模型越可能使用重复的短语
  default: 0.0
  min: -2.0
  max: 2.0
  precision: 0.1
pricing:
  input: '10.8'
  output: '14.4'
  unit: '0.000001'
  currency: CNY
