model: gemini-1.5-flash-002
label:
  en_US: gemini/gemini-1.5-flash-002
model_type: llm
features:
- tool-call
- vision
- audio
- document
- stream-tool-call
- video
model_properties:
  mode: chat
  context_size: 1048576
parameter_rules:
- name: temperature
  type: float
  required: true
  label:
    en_US: 温度temperature
    zh_Hans: 温度temperature
  help:
    en_US: 采样环节的参数，用于控制随机性。较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定
    zh_Hans: 采样环节的参数，用于控制随机性。较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定
  default: 1.0
  min: 0.0
  max: 2.0
  precision: 0.1
pricing:
  input: '0.135'
  output: '0.54'
  unit: '0.000001'
  currency: CNY
