model: deepseek-r1-250528
label:
  en_US: doubao/deepseek-r1-250528
model_type: llm
features:
- stream-tool-call
- tool-call
- agent-thought
model_properties:
  mode: chat
  context_size: 128000
parameter_rules:
- name: temperature
  type: float
  required: false
  label:
    en_US: 温度temperature
    zh_Hans: 温度temperature
  help:
    en_US: 采样温度。控制了生成文本时对每个候选词的概率分布进行平滑的程度。
    zh_Hans: 采样温度。控制了生成文本时对每个候选词的概率分布进行平滑的程度。
  default: 1.0
  min: 0.0
  max: 1.0
  precision: 0.1
- name: top_p
  type: float
  required: false
  label:
    en_US: 核采样top_p
    zh_Hans: 核采样top_p
  help:
    en_US: 控制生成多样性， 影响输出文本的多样性，取值越大，生成文本的多样性越强；建议该参数和temperature只设置其中一个
    zh_Hans: 控制生成多样性， 影响输出文本的多样性，取值越大，生成文本的多样性越强；建议该参数和temperature只设置其中一个
  default: 0.7
  min: 0.0
  max: 1.0
  precision: 0.1
- name: frequency_penalty
  type: float
  required: false
  label:
    en_US: 频率惩罚度
    zh_Hans: 频率惩罚度
  help:
    en_US: 如果值为正，会根据新 token 在文本中的出现频率对其进行惩罚，从而降低模型逐字重复的可能性。
    zh_Hans: 如果值为正，会根据新 token 在文本中的出现频率对其进行惩罚，从而降低模型逐字重复的可能性。
  default: 0.0
  min: -2.0
  max: 2.0
  precision: 0.1
- name: presence_penalty
  type: float
  required: false
  label:
    en_US: 存在惩罚度
    zh_Hans: 存在惩罚度
  help:
    en_US: 如果值为正，会根据新 token 到目前为止是否出现在文本中对其进行惩罚，从而增加模型谈论新主题的可能性。
    zh_Hans: 如果值为正，会根据新 token 到目前为止是否出现在文本中对其进行惩罚，从而增加模型谈论新主题的可能性。
  default: 0.0
  min: -2.0
  max: 2.0
  precision: 0.1
pricing:
  input: '4.0'
  output: '16.0'
  unit: '0.000001'
  currency: CNY
