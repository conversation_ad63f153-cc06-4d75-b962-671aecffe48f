model: step-1.5v-mini
label:
  en_US: stepfun/step-1.5v-mini
model_type: llm
features:
- vision
- video
model_properties:
  mode: chat
  context_size: 32768
parameter_rules:
- name: temperature
  type: float
  required: false
  label:
    en_US: 采样温度
    zh_Hans: 采样温度
  help:
    en_US: 采样温度，介于0.0和2.0之间的数字。较高值（如0.8）会使生成更随机，较低值（如0.2）会使其生成结果更集中且确定。默认值为0.5
    zh_Hans: 采样温度，介于0.0和2.0之间的数字。较高值（如0.8）会使生成更随机，较低值（如0.2）会使其生成结果更集中且确定。默认值为0.5
  default: 0.3
  min: 0.0
  max: 2.0
  precision: 0.1
- name: top_p
  type: float
  required: false
  label:
    en_US: 核心采样
    zh_Hans: 核心采样
  help:
    en_US: 核心采样，该值会使模型生成具有top_p概率质量的标记并输出到结果。默认值为0.9
    zh_Hans: 核心采样，该值会使模型生成具有top_p概率质量的标记并输出到结果。默认值为0.9
  default: 0.9
  min: 0.0
  max: 1.0
  precision: 0.1
- name: frequency_penalty
  type: float
  required: false
  label:
    en_US: 频率惩罚
    zh_Hans: 频率惩罚
  help:
    en_US: 默认为0。介于0.0和1.0之间的数字。值较高会使模型生成某token时，根据其过往在生成文本中出现的频度，进行后续降频惩罚，从而降低模型重复生成相同内容的可能性
    zh_Hans: 默认为0。介于0.0和1.0之间的数字。值较高会使模型生成某token时，根据其过往在生成文本中出现的频度，进行后续降频惩罚，从而降低模型重复生成相同内容的可能性
  default: 0.05
  min: 0.0
  max: 1.0
  precision: 0.01
